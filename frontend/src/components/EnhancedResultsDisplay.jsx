import React, { useState } from 'react';
import { 
  CheckCircle, 
  AlertTriangle, 
  Mail,
  AlertCircle,
  Gavel,
  FileCheck,
  MessageSquare,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  DollarSign,
  Scale,
  Clock,
  Users,
  FileText,
  Eye
} from 'lucide-react';

const EnhancedResultsDisplay = ({ results, selectedUseCase }) => {
  const [expandedSections, setExpandedSections] = useState({
    dataStatus: true,
    legalAnalysis: false,
    lossQuantum: false,
    communications: false,
    workflow: false
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  if (!results || selectedUseCase !== 'uc05') {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Data Sufficiency Status */}
      {results.data_sufficiency && (
        <div className="bg-white rounded-lg shadow-sm border">
          <div 
            className="p-6 cursor-pointer"
            onClick={() => toggleSection('dataStatus')}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <FileCheck className={`h-6 w-6 ${
                  results.data_sufficiency.level === 'complete' ? 'text-green-600' :
                  results.data_sufficiency.level === 'sufficient' ? 'text-yellow-600' :
                  'text-red-600'
                }`} />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Data Sufficiency Assessment
                  </h3>
                  <p className="text-sm text-gray-600">
                    Status: {results.data_sufficiency.level.replace('_', ' ').toUpperCase()}
                  </p>
                </div>
              </div>
              {expandedSections.dataStatus ? <ChevronUp /> : <ChevronDown />}
            </div>
          </div>
          
          {expandedSections.dataStatus && (
            <div className="px-6 pb-6 border-t border-gray-100">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {Math.round(results.data_sufficiency.confidence_score * 100)}%
                  </div>
                  <div className="text-sm text-gray-600">Confidence Score</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-gray-900">
                    {results.data_sufficiency.missing_documents?.length || 0}
                  </div>
                  <div className="text-sm text-gray-600">Missing Documents</div>
                </div>
              </div>
              
              {results.data_sufficiency.missing_documents?.length > 0 && (
                <div className="mt-4">
                  <h4 className="font-medium text-gray-900 mb-2">Missing Documents:</h4>
                  <ul className="space-y-1">
                    {results.data_sufficiency.missing_documents.map((doc, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <AlertCircle className="h-4 w-4 text-red-500" />
                        <span className="text-gray-700">{doc.document_type.replace('_', ' ')}</span>
                        <span className="text-sm text-gray-500">({doc.importance})</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Legal Precedent Analysis */}
      {results.legal_analysis && (
        <div className="bg-white rounded-lg shadow-sm border">
          <div 
            className="p-6 cursor-pointer"
            onClick={() => toggleSection('legalAnalysis')}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Gavel className="h-6 w-6 text-blue-600" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Legal Precedent Analysis
                  </h3>
                  <p className="text-sm text-gray-600">
                    {results.legal_analysis.applicable_precedents?.length || 0} precedents found
                  </p>
                </div>
              </div>
              {expandedSections.legalAnalysis ? <ChevronUp /> : <ChevronDown />}
            </div>
          </div>
          
          {expandedSections.legalAnalysis && (
            <div className="px-6 pb-6 border-t border-gray-100">
              <div className="mt-4 space-y-4">
                {results.legal_analysis.applicable_precedents?.map((precedent, index) => (
                  <div key={index} className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-medium text-blue-900">{precedent.case_name}</h4>
                        <p className="text-sm text-blue-700">{precedent.legal_principle}</p>
                        <div className="mt-2 text-sm text-blue-600">
                          Relevance: {Math.round(precedent.relevance_score * 100)}%
                        </div>
                      </div>
                      <ExternalLink className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>
                ))}
                
                {results.legal_analysis.legal_reasoning && (
                  <div className="mt-4">
                    <h4 className="font-medium text-gray-900 mb-2">Legal Reasoning:</h4>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                        {results.legal_analysis.legal_reasoning}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Loss Quantum Calculation */}
      {results.loss_quantum && (
        <div className="bg-white rounded-lg shadow-sm border">
          <div 
            className="p-6 cursor-pointer"
            onClick={() => toggleSection('lossQuantum')}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <DollarSign className="h-6 w-6 text-green-600" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Loss Quantum Calculation
                  </h3>
                  <p className="text-sm text-gray-600">
                    Settlement: ${results.loss_quantum.settlement_recommendation?.toLocaleString()}
                  </p>
                </div>
              </div>
              {expandedSections.lossQuantum ? <ChevronUp /> : <ChevronDown />}
            </div>
          </div>
          
          {expandedSections.lossQuantum && (
            <div className="px-6 pb-6 border-t border-gray-100">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="bg-gray-50 rounded-lg p-4 text-center">
                  <div className="text-xl font-bold text-gray-900">
                    ${results.loss_quantum.total_damages?.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Total Damages</div>
                </div>
                <div className="bg-red-50 rounded-lg p-4 text-center">
                  <div className="text-xl font-bold text-red-600">
                    ${results.loss_quantum.insured_liability?.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Insured Liability</div>
                </div>
                <div className="bg-green-50 rounded-lg p-4 text-center">
                  <div className="text-xl font-bold text-green-600">
                    ${results.loss_quantum.settlement_recommendation?.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Recommended Settlement</div>
                </div>
              </div>
              
              {results.loss_quantum.damage_breakdown && (
                <div className="mt-4">
                  <h4 className="font-medium text-gray-900 mb-2">Damage Breakdown:</h4>
                  <div className="space-y-2">
                    {results.loss_quantum.damage_breakdown.map((item, index) => (
                      <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100">
                        <span className="text-gray-700">{item.description}</span>
                        <span className="font-medium">${item.amount?.toLocaleString()}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Email Communications */}
      {results.email_drafts && results.email_drafts.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border">
          <div 
            className="p-6 cursor-pointer"
            onClick={() => toggleSection('communications')}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Mail className="h-6 w-6 text-purple-600" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Generated Communications
                  </h3>
                  <p className="text-sm text-gray-600">
                    {results.email_drafts.length} email draft(s) ready
                  </p>
                </div>
              </div>
              {expandedSections.communications ? <ChevronUp /> : <ChevronDown />}
            </div>
          </div>
          
          {expandedSections.communications && (
            <div className="px-6 pb-6 border-t border-gray-100">
              <div className="space-y-4 mt-4">
                {results.email_drafts.map((email, index) => (
                  <div key={index} className="bg-purple-50 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-medium text-purple-900">{email.subject}</h4>
                        <p className="text-sm text-purple-700">
                          To: {email.recipient_type} • Urgency: {email.urgency}
                        </p>
                      </div>
                      <button className="text-purple-600 hover:text-purple-800">
                        <MessageSquare className="h-4 w-4" />
                      </button>
                    </div>
                    <div className="bg-white rounded p-3 text-sm text-gray-700">
                      <pre className="whitespace-pre-wrap">{email.body.substring(0, 200)}...</pre>
                    </div>
                    <div className="mt-2 text-xs text-purple-600">
                      Follow-up in {email.follow_up_days} days
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Workflow Status */}
      {results.workflow_state && (
        <div className="bg-white rounded-lg shadow-sm border">
          <div 
            className="p-6 cursor-pointer"
            onClick={() => toggleSection('workflow')}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Users className="h-6 w-6 text-indigo-600" />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Workflow Status
                  </h3>
                  <p className="text-sm text-gray-600">
                    Current stage: {results.workflow_state.current_stage?.replace('_', ' ')}
                  </p>
                </div>
              </div>
              {expandedSections.workflow ? <ChevronUp /> : <ChevronDown />}
            </div>
          </div>
          
          {expandedSections.workflow && (
            <div className="px-6 pb-6 border-t border-gray-100">
              <div className="mt-4">
                <h4 className="font-medium text-gray-900 mb-2">Next Steps:</h4>
                <ul className="space-y-2">
                  {results.next_steps?.map((step, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-indigo-600" />
                      <span className="text-gray-700">{step}</span>
                    </li>
                  ))}
                </ul>
              </div>
              
              {results.workflow_state.estimated_completion && (
                <div className="mt-4 bg-indigo-50 rounded-lg p-3">
                  <div className="text-sm text-indigo-700">
                    Estimated completion: {new Date(results.workflow_state.estimated_completion).toLocaleString()}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default EnhancedResultsDisplay;
