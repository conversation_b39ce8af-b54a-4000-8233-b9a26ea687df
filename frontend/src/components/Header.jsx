import React from 'react';
import { Menu, Target, Activity, AlertCircle } from 'lucide-react';

const Header = ({ systemStatus, onMenuClick }) => {
  const getStatusIcon = () => {
    switch (systemStatus) {
      case 'healthy':
        return <Activity className="h-4 w-4 text-green-500" />;
      case 'loading':
        return <div className="loading-spinner" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (systemStatus) {
      case 'healthy':
        return 'System Healthy';
      case 'loading':
        return 'Connecting...';
      case 'error':
        return 'System Error';
      default:
        return 'Unknown';
    }
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side */}
          <div className="flex items-center space-x-4">
            <button
              onClick={onMenuClick}
              className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <Menu className="h-6 w-6" />
            </button>
            
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">R</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Rozie AI</h1>
                  <p className="text-xs text-gray-500">Liability Intelligence Platform</p>
                </div>
              </div>
            </div>
          </div>

          {/* Center - Current Use Case */}
          <div className="hidden md:flex items-center space-x-4">
            <div className="flex items-center space-x-2 px-4 py-2 bg-blue-50 rounded-lg">
              <Target className="h-5 w-5 text-blue-600" />
              <span className="text-blue-900 font-medium">Liability Decisions</span>
            </div>
          </div>

          {/* Right side - System Status */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <span className="text-sm text-gray-600 hidden sm:inline">
                {getStatusText()}
              </span>
            </div>
            
            <div className="text-sm text-gray-500">
              <span className="font-medium">v1.0.0</span>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
