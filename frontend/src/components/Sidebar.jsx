import React from 'react';
import { X, Target, FileText } from 'lucide-react';

const Sidebar = ({ isOpen, onClose }) => {

  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
        onClick={onClose}
      />

      {/* Sidebar */}
      <div className="fixed left-0 top-0 h-full w-64 bg-white shadow-lg z-50 transform transition-transform duration-300 ease-in-out">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Navigation</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Current Use Case */}
        <div className="p-4">
          <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">
            Current Solution
          </h3>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-center space-x-3">
              <Target className="h-5 w-5 text-blue-600" />
              <div>
                <div className="font-medium text-blue-900">Liability Decisions</div>
                <div className="text-sm text-blue-700 mt-1">
                  Intelligent fault assessment and liability analysis
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Menu Items */}
        <div className="p-4 border-t border-gray-200">
          <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">
            Menu
          </h3>
          <div className="space-y-1">
            <button className="w-full flex items-center p-2 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
              <FileText className="h-5 w-5 mr-3 text-gray-400" />
              <span>Documents</span>
            </button>
          </div>
        </div>

        {/* Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-center">
            <div className="text-sm font-medium text-gray-900">Rozie AI</div>
            <div className="text-xs text-gray-500">Liability Intelligence Platform</div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
