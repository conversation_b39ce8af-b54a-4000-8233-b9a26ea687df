import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  CheckCircle, 
  AlertTriangle, 
  FileText, 
  Download, 
  Share2, 
  ArrowLeft,
  Target,
  Scale,
  Eye,
  Clock,
  TrendingUp
} from 'lucide-react';
import toast from 'react-hot-toast';
import EnhancedResultsDisplay from '../components/EnhancedResultsDisplay';

const ResultsPage = () => {
  const { jobId } = useParams();
  const navigate = useNavigate();
  
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading results
    const loadResults = async () => {
      setLoading(true);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Generate mock results for liability decisions
      const mockResults = generateMockResults('uc05', jobId);
      setResults(mockResults);
      setLoading(false);
    };

    loadResults();
  }, [jobId]);

  const generateMockResults = (useCase, jobId) => {
    const baseResult = {
      jobId,
      timestamp: new Date().toISOString(),
      processingTime: '2.3s',
      confidence: 0.95,
      status: 'completed'
    };

    if (useCase === 'uc05') {
      return {
        ...baseResult,
        useCase: 'Liability Decisions',
        decision: {
          primaryLiability: 'Party A',
          faultPercentage: {
            partyA: 75,
            partyB: 25
          },
          confidence: 0.92,
          reasoning: [
            'Traffic violation detected in Party A documentation',
            'Witness statements support Party B version of events',
            'Physical evidence indicates Party A was speeding',
            'Party A failed to yield right of way'
          ]
        },
        evidence: {
          documentsAnalyzed: 8,
          keyFindings: [
            'Police report indicates Party A ran red light',
            'Damage patterns consistent with high-speed impact',
            'Witness testimony corroborates Party B account',
            'Traffic camera footage supports findings'
          ]
        },
        recommendations: [
          'Proceed with 75/25 fault allocation',
          'Request additional medical documentation',
          'Consider settlement negotiations',
          'Monitor for potential appeals'
        ],
        // Enhanced production features
        data_sufficiency: {
          level: 'sufficient',
          confidence_score: 0.85,
          missing_documents: [
            { document_type: 'medical_report', importance: 'important' },
            { document_type: 'witness_statement', importance: 'optional' }
          ]
        },
        legal_analysis: {
          applicable_precedents: [
            {
              case_name: 'Thompson v. Metro Grocery Ltd. (2023)',
              legal_principle: 'Occupier\'s liability for known hazards',
              relevance_score: 0.95
            },
            {
              case_name: 'Wilson v. Pacific Shopping Centre (2022)',
              legal_principle: 'Reasonable care in winter maintenance',
              relevance_score: 0.85
            }
          ],
          legal_reasoning: 'Based on Canadian comparative negligence law and similar premises liability cases, the fault allocation is supported by strong legal precedent.'
        },
        loss_quantum: {
          total_damages: 15000,
          insured_liability: 11250,
          settlement_recommendation: 9500,
          damage_breakdown: [
            { description: 'Medical expenses', amount: 5000 },
            { description: 'Lost wages', amount: 3000 },
            { description: 'Pain and suffering', amount: 7000 }
          ]
        },
        email_drafts: [
          {
            subject: 'Additional Medical Documentation Required - Claim #UC05-2024-001',
            body: 'Dear Valued Customer,\n\nThank you for submitting your insurance claim. To complete our assessment, we require additional medical documentation...',
            recipient_type: 'claimant',
            urgency: 'medium',
            follow_up_days: 10
          }
        ],
        workflow_state: {
          current_stage: 'liability_assessment',
          estimated_completion: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString()
        },
        next_steps: [
          'Request additional medical documentation',
          'Proceed with liability analysis',
          'Generate settlement offer',
          'Schedule follow-up review'
        ]
      };
    }

    // Default results for other use cases
    return {
      ...baseResult,
      useCase: 'General Analysis',
      summary: 'Document analysis completed successfully',
      findings: [
        'All documents processed successfully',
        'No anomalies detected',
        'Compliance requirements met',
        'Ready for next steps'
      ]
    };
  };

  const handleDownload = () => {
    toast.success('Results downloaded successfully');
  };

  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href);
    toast.success('Results link copied to clipboard');
  };

  const handleNewAnalysis = () => {
    navigate('/');
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <div className="loading-spinner mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Loading Results...
        </h2>
        <p className="text-gray-600">
          Finalizing analysis for job {jobId}
        </p>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Results Not Found
        </h2>
        <p className="text-gray-600 mb-4">
          Could not load results for job {jobId}
        </p>
        <button
          onClick={() => navigate('/')}
          className="btn-primary"
        >
          Return to Dashboard
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/')}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <ArrowLeft className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Analysis Results
            </h1>
            <p className="text-gray-600">
              {results.useCase} • Job ID: {jobId}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleShare}
            className="btn-secondary flex items-center space-x-2"
          >
            <Share2 className="h-4 w-4" />
            <span>Share</span>
          </button>
          <button
            onClick={handleDownload}
            className="btn-primary flex items-center space-x-2"
          >
            <Download className="h-4 w-4" />
            <span>Download</span>
          </button>
        </div>
      </div>

      {/* Status Banner */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center space-x-3">
          <CheckCircle className="h-6 w-6 text-green-600" />
          <div>
            <h3 className="font-semibold text-green-900">
              Analysis Completed Successfully
            </h3>
            <p className="text-green-700">
              Processed in {results.processingTime} with {Math.round(results.confidence * 100)}% confidence
            </p>
          </div>
        </div>
      </div>

      {/* Results Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Results */}
        <div className="lg:col-span-2 space-y-6">
          {/* Liability Decision */}
          {results.decision && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Scale className="h-6 w-6 text-blue-600" />
                <h2 className="text-xl font-semibold text-gray-900">
                  Liability Decision
                </h2>
              </div>
              
              <div className="space-y-4">
                <div className="bg-blue-50 rounded-lg p-4">
                  <h3 className="font-semibold text-blue-900 mb-2">
                    Fault Allocation
                  </h3>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span>Party A</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-red-500 h-2 rounded-full"
                            style={{ width: `${results.decision.faultPercentage.partyA}%` }}
                          />
                        </div>
                        <span className="font-semibold text-red-600">
                          {results.decision.faultPercentage.partyA}%
                        </span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Party B</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-500 h-2 rounded-full"
                            style={{ width: `${results.decision.faultPercentage.partyB}%` }}
                          />
                        </div>
                        <span className="font-semibold text-green-600">
                          {results.decision.faultPercentage.partyB}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Reasoning</h4>
                  <ul className="space-y-1">
                    {results.decision.reasoning.map((reason, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-blue-600 mt-1">•</span>
                        <span className="text-gray-700">{reason}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Evidence Analysis */}
          {results.evidence && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Eye className="h-6 w-6 text-green-600" />
                <h2 className="text-xl font-semibold text-gray-900">
                  Evidence Analysis
                </h2>
              </div>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-gray-900">
                      {results.evidence.documentsAnalyzed}
                    </div>
                    <div className="text-sm text-gray-600">Documents Analyzed</div>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {Math.round(results.confidence * 100)}%
                    </div>
                    <div className="text-sm text-gray-600">Confidence Score</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Key Findings</h4>
                  <ul className="space-y-2">
                    {results.evidence.keyFindings.map((finding, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{finding}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Recommendations */}
          {results.recommendations && (
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Target className="h-6 w-6 text-purple-600" />
                <h2 className="text-xl font-semibold text-gray-900">
                  Recommendations
                </h2>
              </div>
              
              <ul className="space-y-3">
                {results.recommendations.map((recommendation, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="bg-purple-100 text-purple-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium flex-shrink-0">
                      {index + 1}
                    </div>
                    <span className="text-gray-700">{recommendation}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Enhanced Production Features */}
          <EnhancedResultsDisplay results={results} selectedUseCase="uc05" />
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Processing Summary */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Processing Summary
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Status</span>
                <span className="font-medium text-green-600">Completed</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Processing Time</span>
                <span className="font-medium">{results.processingTime}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Confidence</span>
                <span className="font-medium">{Math.round(results.confidence * 100)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Timestamp</span>
                <span className="font-medium text-sm">
                  {new Date(results.timestamp).toLocaleString()}
                </span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Actions
            </h3>
            <div className="space-y-2">
              <button
                onClick={handleNewAnalysis}
                className="w-full btn-primary"
              >
                New Analysis
              </button>
              <button
                onClick={handleDownload}
                className="w-full btn-secondary"
              >
                Export Results
              </button>
              <button
                onClick={() => window.print()}
                className="w-full btn-secondary"
              >
                Print Report
              </button>
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Performance
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="text-gray-600">Speed</span>
                </div>
                <span className="font-medium text-green-600">Excellent</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="text-gray-600">Accuracy</span>
                </div>
                <span className="font-medium text-green-600">High</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4 text-purple-600" />
                  <span className="text-gray-600">Coverage</span>
                </div>
                <span className="font-medium text-green-600">Complete</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultsPage;
