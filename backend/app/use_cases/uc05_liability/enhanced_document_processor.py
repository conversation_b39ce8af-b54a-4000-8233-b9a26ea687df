"""
🎯 UC05: Enhanced Document Processor
Advanced document classification and specialized evidence extraction for liability claims
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import re
from datetime import datetime

from app.services.ai_service import AIService
from app.services.ocr_service import OCRService
from app.core.config import settings

logger = logging.getLogger(__name__)

class DocumentCategory(Enum):
    """Enhanced document categories"""
    FNOL = "first_notice_of_loss"
    MEDICAL_REPORT = "medical_report"
    POLICE_REPORT = "police_report"
    WITNESS_STATEMENT = "witness_statement"
    INCIDENT_REPORT = "incident_report"
    INSURANCE_CERTIFICATE = "insurance_certificate"
    CORRESPONDENCE = "correspondence"
    FINANCIAL_DOCUMENT = "financial_document"
    PHOTO_EVIDENCE = "photo_evidence"
    LEGAL_DOCUMENT = "legal_document"
    OTHER = "other"

@dataclass
class ExtractedEvidence:
    """Structured evidence extracted from documents"""
    evidence_type: str
    content: str
    confidence: float
    source_document: str
    page_number: Optional[int]
    extraction_method: str
    metadata: Dict[str, Any]

@dataclass
class DocumentAnalysis:
    """Complete document analysis result"""
    document_id: str
    filename: str
    category: DocumentCategory
    classification_confidence: float
    extracted_evidence: List[ExtractedEvidence]
    key_entities: Dict[str, List[str]]
    summary: str
    quality_score: float

class EnhancedDocumentProcessor:
    """Advanced document processing with specialized extractors"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.ocr_service = OCRService()
        self.classification_patterns = self._load_classification_patterns()
        self.extraction_templates = self._load_extraction_templates()
    
    async def process_document(
        self,
        document: Dict[str, Any],
        document_id: str
    ) -> DocumentAnalysis:
        """
        Process a document with enhanced classification and extraction
        
        Args:
            document: Document data with text content
            document_id: Unique document identifier
            
        Returns:
            DocumentAnalysis with classification and extracted evidence
        """
        try:
            logger.info(f"📄 Processing document: {document.get('filename', 'unknown')}")
            
            # Step 1: Enhanced document classification
            category, classification_confidence = await self._classify_document_enhanced(document)
            
            # Step 2: Specialized evidence extraction
            extracted_evidence = await self._extract_evidence_specialized(document, category)
            
            # Step 3: Entity extraction
            key_entities = await self._extract_key_entities(document)
            
            # Step 4: Generate document summary
            summary = await self._generate_document_summary(document, category)
            
            # Step 5: Calculate quality score
            quality_score = await self._calculate_document_quality(document, extracted_evidence)
            
            analysis = DocumentAnalysis(
                document_id=document_id,
                filename=document.get("filename", "unknown"),
                category=category,
                classification_confidence=classification_confidence,
                extracted_evidence=extracted_evidence,
                key_entities=key_entities,
                summary=summary,
                quality_score=quality_score
            )
            
            logger.info(f"✅ Document processed: {category.value} (confidence: {classification_confidence:.2f})")
            return analysis
            
        except Exception as e:
            logger.error(f"❌ Document processing failed: {e}")
            raise
    
    async def _classify_document_enhanced(
        self, document: Dict[str, Any]
    ) -> Tuple[DocumentCategory, float]:
        """Enhanced document classification with pattern matching and AI"""
        
        text = document.get("text", "")
        filename = document.get("filename", "").lower()
        
        # Step 1: Pattern-based classification
        pattern_category, pattern_confidence = self._classify_by_patterns(text, filename)
        
        # Step 2: AI-based classification
        ai_category, ai_confidence = await self._classify_by_ai(text, filename)
        
        # Step 3: Combine results
        if pattern_confidence > 0.8:
            return pattern_category, pattern_confidence
        elif ai_confidence > 0.7:
            return ai_category, ai_confidence
        elif pattern_confidence > ai_confidence:
            return pattern_category, pattern_confidence
        else:
            return ai_category, ai_confidence
    
    def _classify_by_patterns(self, text: str, filename: str) -> Tuple[DocumentCategory, float]:
        """Classify document using pattern matching"""
        
        text_lower = text.lower()
        
        # FNOL patterns
        fnol_patterns = [
            r"first notice of loss", r"fnol", r"claim number", r"incident date",
            r"claimant name", r"policy number"
        ]
        if any(re.search(pattern, text_lower) for pattern in fnol_patterns):
            return DocumentCategory.FNOL, 0.9
        
        # Medical report patterns
        medical_patterns = [
            r"medical report", r"diagnosis", r"patient", r"doctor", r"physician",
            r"hospital", r"treatment", r"medication", r"injury", r"symptoms"
        ]
        if any(re.search(pattern, text_lower) for pattern in medical_patterns):
            return DocumentCategory.MEDICAL_REPORT, 0.85
        
        # Police report patterns
        police_patterns = [
            r"police report", r"incident report", r"officer", r"badge", r"citation",
            r"traffic violation", r"arrest", r"investigation"
        ]
        if any(re.search(pattern, text_lower) for pattern in police_patterns):
            return DocumentCategory.POLICE_REPORT, 0.9
        
        # Witness statement patterns
        witness_patterns = [
            r"witness statement", r"i saw", r"i observed", r"testimony",
            r"statement of", r"sworn statement"
        ]
        if any(re.search(pattern, text_lower) for pattern in witness_patterns):
            return DocumentCategory.WITNESS_STATEMENT, 0.85
        
        # Insurance certificate patterns
        insurance_patterns = [
            r"certificate of insurance", r"policy", r"coverage", r"premium",
            r"insured", r"insurer", r"liability limit"
        ]
        if any(re.search(pattern, text_lower) for pattern in insurance_patterns):
            return DocumentCategory.INSURANCE_CERTIFICATE, 0.8
        
        # Financial document patterns
        financial_patterns = [
            r"invoice", r"receipt", r"bill", r"payment", r"amount due",
            r"total", r"\$[\d,]+", r"cost"
        ]
        if any(re.search(pattern, text_lower) for pattern in financial_patterns):
            return DocumentCategory.FINANCIAL_DOCUMENT, 0.8
        
        # Email/correspondence patterns
        email_patterns = [
            r"from:", r"to:", r"subject:", r"dear", r"sincerely",
            r"@", r"email", r"correspondence"
        ]
        if any(re.search(pattern, text_lower) for pattern in email_patterns):
            return DocumentCategory.CORRESPONDENCE, 0.75
        
        return DocumentCategory.OTHER, 0.3
    
    async def _classify_by_ai(self, text: str, filename: str) -> Tuple[DocumentCategory, float]:
        """Classify document using AI"""
        
        prompt = f"""
        Classify this insurance document into one of these categories:
        
        Categories:
        - first_notice_of_loss: Initial claim reports, FNOL forms
        - medical_report: Medical records, doctor reports, hospital documents
        - police_report: Police incident reports, traffic citations
        - witness_statement: Witness accounts, sworn statements
        - incident_report: Detailed incident descriptions, accident reports
        - insurance_certificate: Insurance policies, certificates, coverage documents
        - correspondence: Emails, letters, communications
        - financial_document: Invoices, receipts, bills, financial records
        - photo_evidence: Photos, images, visual evidence
        - legal_document: Legal filings, court documents, legal correspondence
        - other: Documents that don't fit other categories
        
        Filename: {filename}
        Content (first 1000 chars): {text[:1000]}
        
        Return format: category_name|confidence_score
        Example: medical_report|0.85
        """
        
        try:
            response = await self.ai_service.generate_response(prompt)
            parts = response.strip().split('|')
            if len(parts) == 2:
                category_name = parts[0].strip()
                confidence = float(parts[1].strip())
                
                # Map to enum
                for category in DocumentCategory:
                    if category.value == category_name:
                        return category, confidence
            
            return DocumentCategory.OTHER, 0.5
            
        except Exception as e:
            logger.warning(f"⚠️ AI classification failed: {e}")
            return DocumentCategory.OTHER, 0.3
    
    async def _extract_evidence_specialized(
        self, document: Dict[str, Any], category: DocumentCategory
    ) -> List[ExtractedEvidence]:
        """Extract evidence using specialized extractors"""
        
        evidence_list = []
        text = document.get("text", "")
        filename = document.get("filename", "")
        
        # Use specialized extractor based on document category
        if category == DocumentCategory.MEDICAL_REPORT:
            evidence_list.extend(await self._extract_medical_evidence(text, filename))
        elif category == DocumentCategory.POLICE_REPORT:
            evidence_list.extend(await self._extract_police_evidence(text, filename))
        elif category == DocumentCategory.WITNESS_STATEMENT:
            evidence_list.extend(await self._extract_witness_evidence(text, filename))
        elif category == DocumentCategory.FNOL:
            evidence_list.extend(await self._extract_fnol_evidence(text, filename))
        elif category == DocumentCategory.FINANCIAL_DOCUMENT:
            evidence_list.extend(await self._extract_financial_evidence(text, filename))
        elif category == DocumentCategory.INCIDENT_REPORT:
            evidence_list.extend(await self._extract_incident_evidence(text, filename))
        else:
            evidence_list.extend(await self._extract_general_evidence(text, filename))
        
        return evidence_list
    
    async def _extract_medical_evidence(self, text: str, filename: str) -> List[ExtractedEvidence]:
        """Extract evidence from medical reports"""
        evidence_list = []
        
        # Extract diagnosis
        diagnosis_patterns = [
            r"diagnosis[:\s]+([^\n]+)",
            r"diagnosed with[:\s]+([^\n]+)",
            r"condition[:\s]+([^\n]+)"
        ]
        
        for pattern in diagnosis_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                evidence_list.append(ExtractedEvidence(
                    evidence_type="medical_diagnosis",
                    content=match.strip(),
                    confidence=0.9,
                    source_document=filename,
                    page_number=None,
                    extraction_method="regex_pattern",
                    metadata={"pattern": pattern}
                ))
        
        # Extract treatment information
        treatment_patterns = [
            r"treatment[:\s]+([^\n]+)",
            r"prescribed[:\s]+([^\n]+)",
            r"medication[:\s]+([^\n]+)"
        ]
        
        for pattern in treatment_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                evidence_list.append(ExtractedEvidence(
                    evidence_type="medical_treatment",
                    content=match.strip(),
                    confidence=0.85,
                    source_document=filename,
                    page_number=None,
                    extraction_method="regex_pattern",
                    metadata={"pattern": pattern}
                ))
        
        # Extract injury severity using AI
        injury_evidence = await self._extract_injury_severity_ai(text, filename)
        evidence_list.extend(injury_evidence)
        
        return evidence_list

    async def _extract_injury_severity_ai(self, text: str, filename: str) -> List[ExtractedEvidence]:
        """Extract injury severity using AI analysis"""
        prompt = f"""
        Analyze this medical document and extract injury severity information:

        Text: {text[:2000]}

        Extract:
        1. Type of injury
        2. Severity level (mild/moderate/severe)
        3. Expected recovery time
        4. Functional limitations

        Return as JSON format with confidence scores.
        """

        try:
            response = await self.ai_service.generate_response(prompt)
            injury_data = json.loads(response)

            evidence_list = []
            for key, value in injury_data.items():
                if isinstance(value, dict) and "content" in value:
                    evidence_list.append(ExtractedEvidence(
                        evidence_type=f"injury_{key}",
                        content=value["content"],
                        confidence=value.get("confidence", 0.7),
                        source_document=filename,
                        page_number=None,
                        extraction_method="ai_analysis",
                        metadata={"analysis_type": "injury_severity"}
                    ))

            return evidence_list

        except Exception as e:
            logger.warning(f"⚠️ AI injury extraction failed: {e}")
            return []

    async def _extract_general_evidence(self, text: str, filename: str) -> List[ExtractedEvidence]:
        """Extract general evidence from any document type"""
        evidence_list = []

        # Extract dates
        date_patterns = [
            r"\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b",
            r"\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b"
        ]

        for pattern in date_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                evidence_list.append(ExtractedEvidence(
                    evidence_type="date",
                    content=match,
                    confidence=0.9,
                    source_document=filename,
                    page_number=None,
                    extraction_method="regex_pattern",
                    metadata={"pattern": pattern}
                ))

        # Extract names (basic pattern)
        name_patterns = [
            r"\b[A-Z][a-z]+ [A-Z][a-z]+\b"
        ]

        for pattern in name_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                evidence_list.append(ExtractedEvidence(
                    evidence_type="person_name",
                    content=match,
                    confidence=0.7,
                    source_document=filename,
                    page_number=None,
                    extraction_method="regex_pattern",
                    metadata={"pattern": pattern}
                ))

        return evidence_list

    async def _extract_key_entities(self, document: Dict[str, Any]) -> Dict[str, List[str]]:
        """Extract key entities from document"""
        text = document.get("text", "")

        entities = {
            "people": [],
            "organizations": [],
            "locations": [],
            "dates": [],
            "amounts": []
        }

        # Extract people names
        name_pattern = r"\b[A-Z][a-z]+ [A-Z][a-z]+\b"
        entities["people"] = list(set(re.findall(name_pattern, text)))

        # Extract organizations
        org_patterns = [
            r"\b[A-Z][a-z]+ (?:Inc|Corp|Ltd|LLC|Company|Insurance)\b",
            r"\b(?:Hospital|Medical Center|Clinic)\b"
        ]
        for pattern in org_patterns:
            entities["organizations"].extend(re.findall(pattern, text))

        # Extract locations
        location_patterns = [
            r"\b\d+ [A-Z][a-z]+ (?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr)\b",
            r"\b[A-Z][a-z]+, [A-Z]{2}\b"
        ]
        for pattern in location_patterns:
            entities["locations"].extend(re.findall(pattern, text))

        # Extract dates
        date_patterns = [
            r"\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b",
            r"\b(?:January|February|March|April|May|June|July|August|September|October|November|December) \d{1,2}, \d{4}\b"
        ]
        for pattern in date_patterns:
            entities["dates"].extend(re.findall(pattern, text))

        # Extract monetary amounts
        amount_patterns = [
            r"\$[\d,]+\.?\d*"
        ]
        for pattern in amount_patterns:
            entities["amounts"].extend(re.findall(pattern, text))

        # Remove duplicates and limit results
        for key in entities:
            entities[key] = list(set(entities[key]))[:10]  # Limit to 10 items per category

        return entities

    async def _generate_document_summary(
        self, document: Dict[str, Any], category: DocumentCategory
    ) -> str:
        """Generate a summary of the document"""
        text = document.get("text", "")[:2000]  # First 2000 chars

        prompt = f"""
        Generate a concise summary of this {category.value.replace('_', ' ')} document:

        Text: {text}

        Focus on:
        - Key facts relevant to liability assessment
        - Important dates and parties
        - Critical information for claims processing

        Keep summary under 200 words.
        """

        try:
            summary = await self.ai_service.generate_response(prompt)
            return summary.strip()
        except Exception as e:
            logger.warning(f"⚠️ Summary generation failed: {e}")
            return f"Summary unavailable for {category.value} document"

    async def _calculate_document_quality(
        self, document: Dict[str, Any], extracted_evidence: List[ExtractedEvidence]
    ) -> float:
        """Calculate document quality score"""

        # Base quality factors
        text_length = len(document.get("text", ""))
        ocr_confidence = document.get("confidence", 0.5)
        evidence_count = len(extracted_evidence)

        # Calculate quality score
        length_score = min(text_length / 1000, 1.0)  # Normalize to 1000 chars
        evidence_score = min(evidence_count / 5, 1.0)  # Normalize to 5 pieces of evidence

        # Weighted average
        quality_score = (
            0.3 * ocr_confidence +
            0.3 * length_score +
            0.4 * evidence_score
        )

        return round(quality_score, 2)

    def _load_classification_patterns(self) -> Dict[str, List[str]]:
        """Load classification patterns"""
        return {
            "medical": ["diagnosis", "treatment", "patient", "doctor", "hospital"],
            "police": ["officer", "citation", "violation", "incident report"],
            "financial": ["invoice", "receipt", "payment", "amount", "total"],
            "legal": ["court", "attorney", "lawsuit", "legal", "judgment"]
        }

    def _load_extraction_templates(self) -> Dict[str, Dict[str, str]]:
        """Load extraction templates"""
        return {
            "medical_report": {
                "diagnosis": r"diagnosis[:\s]+([^\n]+)",
                "treatment": r"treatment[:\s]+([^\n]+)",
                "medication": r"medication[:\s]+([^\n]+)"
            },
            "police_report": {
                "incident_time": r"time[:\s]+([^\n]+)",
                "location": r"location[:\s]+([^\n]+)",
                "violation": r"violation[:\s]+([^\n]+)"
            }
        }
